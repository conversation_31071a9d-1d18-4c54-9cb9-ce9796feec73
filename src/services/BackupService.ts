import { Guild, TextChannel } from 'discord.js';
import { BackupData, BackupType, ChannelBackup, RoleBackup, EmojiBackup, MessageBackup } from '../types/Backup';
import { FileUtils } from '../utils/FileUtils';

export class BackupService {
    public async backupAll(guild: Guild): Promise<string> {
        const backupData: BackupData = {
            guildId: guild.id,
            guildName: guild.name,
            timestamp: new Date().toISOString(),
            type: BackupType.ALL,
            data: {
                channels: await this.getChannelData(guild),
                roles: await this.getRoleData(guild),
                emojis: await this.getEmojiData(guild),
                settings: await this.getGuildSettings(guild),
                status: await this.getGuildStatus(guild)
            }
        };

        const filename = FileUtils.generateBackupFilename(guild.id, 'all');
        await FileUtils.saveBackup(filename, backupData);
        return filename;
    }

    public async backupChannels(guild: Guild): Promise<string> {
        const channels = await this.getChannelData(guild);
        
        const backupData: BackupData = {
            guildId: guild.id,
            guildName: guild.name,
            timestamp: new Date().toISOString(),
            type: BackupType.CHANNEL,
            data: { channels }
        };

        const filename = FileUtils.generateBackupFilename(guild.id, 'channels');
        await FileUtils.saveBackup(filename, backupData);
        return filename;
    }

    public async backupEmojis(guild: Guild): Promise<string> {
        const emojis = await this.getEmojiData(guild);
        
        const backupData: BackupData = {
            guildId: guild.id,
            guildName: guild.name,
            timestamp: new Date().toISOString(),
            type: BackupType.EMOJI,
            data: { emojis }
        };

        const filename = FileUtils.generateBackupFilename(guild.id, 'emojis');
        await FileUtils.saveBackup(filename, backupData);
        return filename;
    }

    public async backupProvice(guild: Guild): Promise<string> {
        const settings = await this.getGuildSettings(guild);
        
        const backupData: BackupData = {
            guildId: guild.id,
            guildName: guild.name,
            timestamp: new Date().toISOString(),
            type: BackupType.PROVICE,
            data: { settings }
        };

        const filename = FileUtils.generateBackupFilename(guild.id, 'provice');
        await FileUtils.saveBackup(filename, backupData);
        return filename;
    }

    public async backupChat(guild: Guild, channelId?: string): Promise<string> {
        const messages = await this.getChatData(guild, channelId);
        
        const backupData: BackupData = {
            guildId: guild.id,
            guildName: guild.name,
            timestamp: new Date().toISOString(),
            type: BackupType.CHAT,
            data: { messages, channelId }
        };

        const filename = FileUtils.generateBackupFilename(guild.id, 'chat');
        await FileUtils.saveBackup(filename, backupData);
        return filename;
    }

    public async backupRoles(guild: Guild): Promise<string> {
        const roles = await this.getRoleData(guild);
        
        const backupData: BackupData = {
            guildId: guild.id,
            guildName: guild.name,
            timestamp: new Date().toISOString(),
            type: BackupType.ROLE,
            data: { roles }
        };

        const filename = FileUtils.generateBackupFilename(guild.id, 'roles');
        await FileUtils.saveBackup(filename, backupData);
        return filename;
    }

    public async backupStatus(guild: Guild): Promise<string> {
        const status = await this.getGuildStatus(guild);

        const backupData: BackupData = {
            guildId: guild.id,
            guildName: guild.name,
            timestamp: new Date().toISOString(),
            type: BackupType.STATUS,
            data: { status }
        };

        const filename = FileUtils.generateBackupFilename(guild.id, 'status');
        await FileUtils.saveBackup(filename, backupData);
        return filename;
    }

    private async getChannelData(guild: Guild): Promise<ChannelBackup[]> {
        const channels: ChannelBackup[] = [];

        for (const [, channel] of guild.channels.cache) {
            const channelData: ChannelBackup = {
                id: channel.id,
                name: channel.name,
                type: channel.type,
            };

            if ('position' in channel) {
                channelData.position = channel.position;
            }

            if (channel.parentId) {
                channelData.parentId = channel.parentId;
            }

            if ('permissionOverwrites' in channel) {
                channelData.permissionOverwrites = channel.permissionOverwrites.cache.map((overwrite: any) => ({
                    id: overwrite.id,
                    type: overwrite.type,
                    allow: overwrite.allow.toArray(),
                    deny: overwrite.deny.toArray()
                }));
            }

            if (channel instanceof TextChannel) {
                if (channel.topic) {
                    channelData.topic = channel.topic;
                }
                channelData.nsfw = channel.nsfw;
            }

            channels.push(channelData);
        }

        return channels;
    }

    private async getRoleData(guild: Guild): Promise<RoleBackup[]> {
        const roles: RoleBackup[] = [];

        for (const [, role] of guild.roles.cache) {
            if (role.name === '@everyone') continue;

            roles.push({
                id: role.id,
                name: role.name,
                color: role.color,
                hoist: role.hoist,
                mentionable: role.mentionable,
                permissions: role.permissions.bitfield.toString(),
                position: role.position
            });
        }

        return roles;
    }

    private async getEmojiData(guild: Guild): Promise<EmojiBackup[]> {
        const emojis: EmojiBackup[] = [];

        for (const [, emoji] of guild.emojis.cache) {
            emojis.push({
                id: emoji.id!,
                name: emoji.name!,
                url: emoji.url,
                animated: emoji.animated!
            });
        }

        return emojis;
    }

    private async getGuildSettings(guild: Guild): Promise<any> {
        return {
            name: guild.name,
            description: guild.description,
            icon: guild.iconURL(),
            banner: guild.bannerURL(),
            verificationLevel: guild.verificationLevel,
            defaultMessageNotifications: guild.defaultMessageNotifications,
            explicitContentFilter: guild.explicitContentFilter,
            mfaLevel: guild.mfaLevel,
            systemChannelId: guild.systemChannelId,
            rulesChannelId: guild.rulesChannelId,
            publicUpdatesChannelId: guild.publicUpdatesChannelId,
            preferredLocale: guild.preferredLocale,
            features: guild.features
        };
    }

    private async getGuildStatus(guild: Guild): Promise<any> {
        return {
            memberCount: guild.memberCount,
            onlineMembers: guild.approximatePresenceCount,
            premiumTier: guild.premiumTier,
            premiumSubscriptionCount: guild.premiumSubscriptionCount,
            large: guild.large,
            createdTimestamp: guild.createdTimestamp,
            ownerId: guild.ownerId
        };
    }

    private async getChatData(guild: Guild, channelId?: string): Promise<MessageBackup[]> {
        const messages: MessageBackup[] = [];
        const channels = channelId
            ? [guild.channels.cache.get(channelId)]
            : guild.channels.cache.filter(ch => ch instanceof TextChannel).values();

        for (const channel of channels) {
            if (!channel || !(channel instanceof TextChannel)) continue;

            try {
                const fetchedMessages = await channel.messages.fetch({ limit: 100 });

                for (const [, message] of fetchedMessages) {
                    messages.push({
                        id: message.id,
                        content: message.content,
                        author: {
                            id: message.author.id,
                            username: message.author.username,
                            discriminator: message.author.discriminator
                        },
                        timestamp: message.createdAt.toISOString(),
                        attachments: message.attachments.map(att => ({
                            id: att.id,
                            name: att.name,
                            url: att.url,
                            size: att.size
                        })),
                        embeds: message.embeds.map(embed => embed.toJSON())
                    });
                }
            } catch (error) {
                console.error(`Failed to fetch messages from channel ${channel.name}:`, error);
            }
        }

        return messages;
    }
}
