import * as cron from 'node-cron';
import { Client } from 'discord.js';
import { BackupService } from './BackupService';

export class AutoBackupService {
    private client: Client;
    private backupService: BackupService;
    private isRunning: boolean = false;

    constructor(client: Client) {
        this.client = client;
        this.backupService = new BackupService();
    }

    public start(): void {
        if (this.isRunning) {
            console.log('AutoBackup service is already running');
            return;
        }

        cron.schedule('0 0 * * *', async () => {
            console.log('Starting daily auto-backup...');
            await this.performDailyBackup();
        }, {
            scheduled: true,
            timezone: 'UTC'
        });

        this.isRunning = true;
        console.log('AutoBackup service started - Daily backups scheduled for midnight UTC');
    }

    public stop(): void {
        if (!this.isRunning) {
            console.log('AutoBackup service is not running');
            return;
        }

        cron.destroy();
        this.isRunning = false;
        console.log('AutoBackup service stopped');
    }

    private async performDailyBackup(): Promise<void> {
        const guilds = this.client.guilds.cache;
        
        if (guilds.size === 0) {
            console.log('No guilds found for auto-backup');
            return;
        }

        console.log(`Starting auto-backup for ${guilds.size} guild(s)`);

        for (const [guildId, guild] of guilds) {
            try {
                console.log(`Backing up guild: ${guild.name} (${guildId})`);
                
                const filename = await this.backupService.backupAll(guild);
                console.log(`Auto-backup completed for ${guild.name}: ${filename}`);
                
                await this.cleanupOldBackups(guildId);
            } catch (error) {
                console.error(`Auto-backup failed for guild ${guild.name} (${guildId}):`, error);
            }
        }

        console.log('Daily auto-backup completed');
    }

    private async cleanupOldBackups(guildId: string): Promise<void> {
        try {
            const { promises: fs } = await import('fs');
            const { join } = await import('path');
            
            const backupDir = join(process.cwd(), 'backups');
            const files = await fs.readdir(backupDir);
            
            const guildBackups = files
                .filter(file => file.includes(guildId) && file.includes('all-'))
                .map(file => ({
                    name: file,
                    path: join(backupDir, file),
                    stats: null as any
                }));

            for (const backup of guildBackups) {
                backup.stats = await fs.stat(backup.path);
            }

            guildBackups.sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime());

            const backupsToDelete = guildBackups.slice(7);

            for (const backup of backupsToDelete) {
                try {
                    await fs.unlink(backup.path);
                    console.log(`Deleted old backup: ${backup.name}`);
                } catch (error) {
                    console.error(`Failed to delete backup ${backup.name}:`, error);
                }
            }

            if (backupsToDelete.length > 0) {
                console.log(`Cleaned up ${backupsToDelete.length} old backup(s) for guild ${guildId}`);
            }
        } catch (error) {
            console.error(`Failed to cleanup old backups for guild ${guildId}:`, error);
        }
    }

    public async performManualBackup(guildId: string): Promise<string | null> {
        const guild = this.client.guilds.cache.get(guildId);
        
        if (!guild) {
            throw new Error('Guild not found');
        }

        try {
            const filename = await this.backupService.backupAll(guild);
            console.log(`Manual backup completed for ${guild.name}: ${filename}`);
            return filename;
        } catch (error) {
            console.error(`Manual backup failed for guild ${guild.name}:`, error);
            throw error;
        }
    }

    public getStatus(): { isRunning: boolean; nextRun?: string } {
        return {
            isRunning: this.isRunning,
            nextRun: this.isRunning ? 'Daily at 00:00 UTC' : undefined
        };
    }
}
