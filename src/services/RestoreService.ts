import { Guild, ChannelType, PermissionFlagsBits, TextChannel, VoiceChannel, CategoryChannel } from 'discord.js';
import { BackupData, BackupType, ChannelBackup, RoleBackup, EmojiBackup } from '../types/Backup';
import { FileUtils } from '../utils/FileUtils';

export class RestoreService {
    public async restoreFromFile(guild: Guild, filename: string): Promise<void> {
        const backupData: BackupData = await FileUtils.loadBackup(filename);
        
        if (backupData.guildId !== guild.id) {
            throw new Error('Backup file is for a different server!');
        }

        switch (backupData.type) {
            case BackupType.ALL:
                await this.restoreAll(guild, backupData);
                break;
            case BackupType.CHANNEL:
                await this.restoreChannels(guild, backupData.data.channels);
                break;
            case BackupType.EMOJI:
                await this.restoreEmojis(guild, backupData.data.emojis);
                break;
            case BackupType.ROLE:
                await this.restoreRoles(guild, backupData.data.roles);
                break;
            default:
                throw new Error(`Restore type ${backupData.type} is not supported yet`);
        }
    }

    public async listAvailableBackups(guildId: string): Promise<string[]> {
        const allBackups = await FileUtils.listBackups();
        return allBackups.filter(filename => filename.includes(guildId));
    }

    private async restoreAll(guild: Guild, backupData: BackupData): Promise<void> {
        const data = backupData.data;
        
        if (data.channels) {
            await this.restoreChannels(guild, data.channels);
        }
        
        if (data.roles) {
            await this.restoreRoles(guild, data.roles);
        }
        
        if (data.emojis) {
            await this.restoreEmojis(guild, data.emojis);
        }
    }

    private async restoreChannels(guild: Guild, channels: ChannelBackup[]): Promise<void> {
        const existingChannels = guild.channels.cache;
        const channelsToCreate = channels.filter(ch => !existingChannels.has(ch.id));
        
        const categoryMap = new Map<string, string>();
        
        const sortedChannels = channelsToCreate.sort((a, b) => {
            if (a.type === ChannelType.GuildCategory && b.type !== ChannelType.GuildCategory) return -1;
            if (a.type !== ChannelType.GuildCategory && b.type === ChannelType.GuildCategory) return 1;
            return a.position - b.position;
        });

        for (const channelData of sortedChannels) {
            try {
                let parent = null;
                if (channelData.parentId && categoryMap.has(channelData.parentId)) {
                    parent = categoryMap.get(channelData.parentId)!;
                }

                const options: any = {
                    name: channelData.name,
                    type: channelData.type,
                    position: channelData.position,
                    parent: parent
                };

                if (channelData.topic && channelData.type === ChannelType.GuildText) {
                    options.topic = channelData.topic;
                }

                if (channelData.nsfw !== undefined) {
                    options.nsfw = channelData.nsfw;
                }

                const createdChannel = await guild.channels.create(options);
                
                if (channelData.type === ChannelType.GuildCategory) {
                    categoryMap.set(channelData.id, createdChannel.id);
                }

                for (const overwrite of channelData.permissionOverwrites) {
                    try {
                        await createdChannel.permissionOverwrites.create(overwrite.id, {
                            allow: overwrite.allow,
                            deny: overwrite.deny
                        });
                    } catch (error) {
                        console.error(`Failed to set permissions for channel ${channelData.name}:`, error);
                    }
                }
            } catch (error) {
                console.error(`Failed to create channel ${channelData.name}:`, error);
            }
        }
    }

    private async restoreRoles(guild: Guild, roles: RoleBackup[]): Promise<void> {
        const existingRoles = guild.roles.cache;
        const rolesToCreate = roles.filter(role => !existingRoles.has(role.id));
        
        const sortedRoles = rolesToCreate.sort((a, b) => a.position - b.position);

        for (const roleData of sortedRoles) {
            try {
                await guild.roles.create({
                    name: roleData.name,
                    color: roleData.color,
                    hoist: roleData.hoist,
                    mentionable: roleData.mentionable,
                    permissions: BigInt(roleData.permissions),
                    position: roleData.position
                });
            } catch (error) {
                console.error(`Failed to create role ${roleData.name}:`, error);
            }
        }
    }

    private async restoreEmojis(guild: Guild, emojis: EmojiBackup[]): Promise<void> {
        const existingEmojis = guild.emojis.cache;
        const emojisToCreate = emojis.filter(emoji => !existingEmojis.has(emoji.id));

        for (const emojiData of emojisToCreate) {
            try {
                await guild.emojis.create({
                    attachment: emojiData.url,
                    name: emojiData.name
                });
            } catch (error) {
                console.error(`Failed to create emoji ${emojiData.name}:`, error);
            }
        }
    }
}
