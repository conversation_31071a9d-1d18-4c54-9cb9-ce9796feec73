import { ChatInputCommandInteraction, SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder } from 'discord.js';
import { ICommand } from '../types/Command';
import { BackupService } from '../services/BackupService';

export default class BackupCommand implements ICommand {
    private backupService: BackupService;

    constructor() {
        this.backupService = new BackupService();
    }

    public data = new SlashCommandBuilder()
        .setName('backup')
        .setDescription('Backs up server data to JSON files')
        .addSubcommand(subcommand =>
            subcommand
                .setName('all')
                .setDescription('Backs up all server data')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('channel')
                .setDescription('Backs up channel settings and messages')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('emoji')
                .setDescription('Backs up custom emojis')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('provice')
                .setDescription('Backs up server privacy settings')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('chat')
                .setDescription('Backs up messages in specified channels')
                .addChannelOption(option =>
                    option
                        .setName('channel')
                        .setDescription('Specific channel to backup (optional)')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('role')
                .setDescription('Backs up role settings')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('status')
                .setDescription('Backs up server status information')
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator);

    public async run(interaction: ChatInputCommandInteraction): Promise<void> {
        const guild = interaction.guild;
        if (!guild) {
            await interaction.reply({ content: 'This command can only be used in a server!', ephemeral: true });
            return;
        }

        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            await interaction.reply({ content: 'You need Administrator permissions to use this command!', ephemeral: true });
            return;
        }

        const subcommand = interaction.options.getSubcommand();
        
        await interaction.deferReply();

        try {
            let filename: string;
            let backupType: string;

            switch (subcommand) {
                case 'all':
                    filename = await this.backupService.backupAll(guild);
                    backupType = 'All server data';
                    break;
                case 'channel':
                    filename = await this.backupService.backupChannels(guild);
                    backupType = 'Channel settings';
                    break;
                case 'emoji':
                    filename = await this.backupService.backupEmojis(guild);
                    backupType = 'Custom emojis';
                    break;
                case 'provice':
                    filename = await this.backupService.backupProvice(guild);
                    backupType = 'Server privacy settings';
                    break;
                case 'chat':
                    const channel = interaction.options.getChannel('channel');
                    filename = await this.backupService.backupChat(guild, channel?.id);
                    backupType = channel ? `Messages from ${channel.name}` : 'All chat messages';
                    break;
                case 'role':
                    filename = await this.backupService.backupRoles(guild);
                    backupType = 'Role settings';
                    break;
                case 'status':
                    filename = await this.backupService.backupStatus(guild);
                    backupType = 'Server status';
                    break;
                default:
                    await interaction.editReply('Invalid backup type!');
                    return;
            }

            const embed = new EmbedBuilder()
                .setColor(0x00FF00)
                .setTitle('Backup Successful')
                .setDescription(`${backupType} has been backed up successfully!`)
                .addFields(
                    { name: 'Filename', value: filename, inline: true },
                    { name: 'Server', value: guild.name, inline: true },
                    { name: 'Timestamp', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: false }
                )
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });
        } catch (error) {
            console.error('Backup error:', error);
            await interaction.editReply('An error occurred while creating the backup!');
        }
    }
}
