import { CommandInteraction, PermissionFlagsBits, EmbedBuilder, Channel, ApplicationCommandOptionType } from 'discord.js';
import { Discord, Slash, SlashGroup, SlashOption } from 'discordx';
import { BackupService } from '../services/BackupService';

@Discord()
@SlashGroup({
    description: 'Backs up server data to JSON files',
    name: 'backup',
    defaultMemberPermissions: PermissionFlagsBits.Administrator
})
export class BackupCommand {
    private backupService: BackupService;

    constructor() {
        this.backupService = new BackupService();
    }

    @SlashGroup('backup')
    @Slash({ description: 'Backs up all server data' })
    async all(interaction: CommandInteraction): Promise<void> {
        await this.handleBackupAll(interaction);
    }

    @SlashGroup('backup')
    @Slash({ description: 'Backs up channel settings and messages' })
    async channel(interaction: CommandInteraction): Promise<void> {
        await this.handleBackupChannel(interaction);
    }

    @SlashGroup('backup')
    @Slash({ description: 'Backs up custom emojis' })
    async emoji(interaction: CommandInteraction): Promise<void> {
        await this.handleBackupEmoji(interaction);
    }

    @SlashGroup('backup')
    @Slash({ description: 'Backs up server privacy settings' })
    async provice(interaction: CommandInteraction): Promise<void> {
        await this.handleBackupProvice(interaction);
    }

    @SlashGroup('backup')
    @Slash({ description: 'Backs up messages in specified channels' })
    async chat(
        @SlashOption({
            name: 'channel',
            description: 'Specific channel to backup (optional)',
            type: ApplicationCommandOptionType.Channel,
            required: false
        })
        channel: Channel | undefined,
        interaction: CommandInteraction
    ): Promise<void> {
        await this.handleBackupChat(interaction, channel);
    }

    @SlashGroup('backup')
    @Slash({ description: 'Backs up role settings' })
    async role(interaction: CommandInteraction): Promise<void> {
        await this.handleBackupRole(interaction);
    }

    @SlashGroup('backup')
    @Slash({ description: 'Backs up server status information' })
    async status(interaction: CommandInteraction): Promise<void> {
        await this.handleBackupStatus(interaction);
    }

    private async handleBackupAll(interaction: CommandInteraction): Promise<void> {
        const guild = interaction.guild;
        if (!this.validateGuildAndPermissions(interaction, guild)) return;

        await interaction.deferReply();
        try {
            const filename = await this.backupService.backupAll(guild!);
            await this.sendSuccessEmbed(interaction, 'All server data', filename, guild!.name);
        } catch (error) {
            await this.handleError(interaction, error);
        }
    }

    private async handleBackupChannel(interaction: CommandInteraction): Promise<void> {
        const guild = interaction.guild;
        if (!this.validateGuildAndPermissions(interaction, guild)) return;

        await interaction.deferReply();
        try {
            const filename = await this.backupService.backupChannels(guild!);
            await this.sendSuccessEmbed(interaction, 'Channel settings', filename, guild!.name);
        } catch (error) {
            await this.handleError(interaction, error);
        }
    }

    private async handleBackupEmoji(interaction: CommandInteraction): Promise<void> {
        const guild = interaction.guild;
        if (!this.validateGuildAndPermissions(interaction, guild)) return;

        await interaction.deferReply();
        try {
            const filename = await this.backupService.backupEmojis(guild!);
            await this.sendSuccessEmbed(interaction, 'Custom emojis', filename, guild!.name);
        } catch (error) {
            await this.handleError(interaction, error);
        }
    }

    private async handleBackupProvice(interaction: CommandInteraction): Promise<void> {
        const guild = interaction.guild;
        if (!this.validateGuildAndPermissions(interaction, guild)) return;

        await interaction.deferReply();
        try {
            const filename = await this.backupService.backupProvice(guild!);
            await this.sendSuccessEmbed(interaction, 'Server privacy settings', filename, guild!.name);
        } catch (error) {
            await this.handleError(interaction, error);
        }
    }

    private async handleBackupChat(interaction: CommandInteraction, channel?: Channel): Promise<void> {
        const guild = interaction.guild;
        if (!this.validateGuildAndPermissions(interaction, guild)) return;

        await interaction.deferReply();
        try {
            const filename = await this.backupService.backupChat(guild!, channel?.id);
            const backupType = channel ? `Messages from ${channel.name}` : 'All chat messages';
            await this.sendSuccessEmbed(interaction, backupType, filename, guild!.name);
        } catch (error) {
            await this.handleError(interaction, error);
        }
    }

    private async handleBackupRole(interaction: CommandInteraction): Promise<void> {
        const guild = interaction.guild;
        if (!this.validateGuildAndPermissions(interaction, guild)) return;

        await interaction.deferReply();
        try {
            const filename = await this.backupService.backupRoles(guild!);
            await this.sendSuccessEmbed(interaction, 'Role settings', filename, guild!.name);
        } catch (error) {
            await this.handleError(interaction, error);
        }
    }

    private async handleBackupStatus(interaction: CommandInteraction): Promise<void> {
        const guild = interaction.guild;
        if (!this.validateGuildAndPermissions(interaction, guild)) return;

        await interaction.deferReply();
        try {
            const filename = await this.backupService.backupStatus(guild!);
            await this.sendSuccessEmbed(interaction, 'Server status', filename, guild!.name);
        } catch (error) {
            await this.handleError(interaction, error);
        }
    }

    private validateGuildAndPermissions(interaction: CommandInteraction, guild: any): boolean {
        if (!guild) {
            interaction.reply({ content: 'This command can only be used in a server!', ephemeral: true });
            return false;
        }

        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            interaction.reply({ content: 'You need Administrator permissions to use this command!', ephemeral: true });
            return false;
        }

        return true;
    }

    private async sendSuccessEmbed(interaction: CommandInteraction, backupType: string, filename: string, guildName: string): Promise<void> {
        const embed = new EmbedBuilder()
            .setColor(0x00FF00)
            .setTitle('Backup Successful')
            .setDescription(`${backupType} has been backed up successfully!`)
            .addFields(
                { name: 'Filename', value: filename, inline: true },
                { name: 'Server', value: guildName, inline: true },
                { name: 'Timestamp', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: false }
            )
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });
    }

    private async handleError(interaction: CommandInteraction, error: any): Promise<void> {
        console.error('Backup error:', error);
        await interaction.editReply('An error occurred while creating the backup!');
    }
}
