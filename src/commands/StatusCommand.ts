import { ChatInputCommandInteraction, SlashCommandBuilder, EmbedBuilder, ChannelType } from 'discord.js';
import { ICommand } from '../types/Command';

export default class StatusCommand implements ICommand {
    public data = new SlashCommandBuilder()
        .setName('status')
        .setDescription('Displays bot or server information')
        .addSubcommand(subcommand =>
            subcommand
                .setName('profile')
                .setDescription('Shows bot username, ID, and creation date')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('server')
                .setDescription('Shows server name, member count, and creation date')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('channel')
                .setDescription('Shows details of the current channel')
        );

    public async run(interaction: ChatInputCommandInteraction): Promise<void> {
        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'profile':
                await this.handleProfileStatus(interaction);
                break;
            case 'server':
                await this.handleServerStatus(interaction);
                break;
            case 'channel':
                await this.handleChannelStatus(interaction);
                break;
        }
    }

    private async handleProfileStatus(interaction: ChatInputCommandInteraction): Promise<void> {
        const bot = interaction.client.user;
        if (!bot) return;

        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle('Bot Profile')
            .setThumbnail(bot.displayAvatarURL())
            .addFields(
                { name: 'Username', value: bot.username, inline: true },
                { name: 'ID', value: bot.id, inline: true },
                { name: 'Created', value: `<t:${Math.floor(bot.createdTimestamp / 1000)}:F>`, inline: false },
                { name: 'Servers', value: interaction.client.guilds.cache.size.toString(), inline: true },
                { name: 'Users', value: interaction.client.users.cache.size.toString(), inline: true }
            )
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });
    }

    private async handleServerStatus(interaction: ChatInputCommandInteraction): Promise<void> {
        const guild = interaction.guild;
        if (!guild) {
            await interaction.reply({ content: 'This command can only be used in a server!', ephemeral: true });
            return;
        }

        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle('Server Information')
            .setThumbnail(guild.iconURL())
            .addFields(
                { name: 'Server Name', value: guild.name, inline: true },
                { name: 'Server ID', value: guild.id, inline: true },
                { name: 'Created', value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:F>`, inline: false },
                { name: 'Owner', value: `<@${guild.ownerId}>`, inline: true },
                { name: 'Members', value: guild.memberCount.toString(), inline: true },
                { name: 'Channels', value: guild.channels.cache.size.toString(), inline: true },
                { name: 'Roles', value: guild.roles.cache.size.toString(), inline: true },
                { name: 'Emojis', value: guild.emojis.cache.size.toString(), inline: true },
                { name: 'Boost Level', value: guild.premiumTier.toString(), inline: true }
            )
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });
    }

    private async handleChannelStatus(interaction: ChatInputCommandInteraction): Promise<void> {
        const channel = interaction.channel;
        if (!channel) return;

        const channelTypeNames: Record<ChannelType, string> = {
            [ChannelType.GuildText]: 'Text Channel',
            [ChannelType.DM]: 'Direct Message',
            [ChannelType.GuildVoice]: 'Voice Channel',
            [ChannelType.GroupDM]: 'Group DM',
            [ChannelType.GuildCategory]: 'Category',
            [ChannelType.GuildAnnouncement]: 'Announcement Channel',
            [ChannelType.AnnouncementThread]: 'Announcement Thread',
            [ChannelType.PublicThread]: 'Public Thread',
            [ChannelType.PrivateThread]: 'Private Thread',
            [ChannelType.GuildStageVoice]: 'Stage Channel',
            [ChannelType.GuildDirectory]: 'Directory',
            [ChannelType.GuildForum]: 'Forum Channel',
            [ChannelType.GuildMedia]: 'Media Channel'
        };

        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle('Channel Information')
            .addFields(
                { name: 'Channel Name', value: channel.name || 'Unknown', inline: true },
                { name: 'Channel ID', value: channel.id, inline: true },
                { name: 'Type', value: channelTypeNames[channel.type] || 'Unknown', inline: true },
                { name: 'Created', value: `<t:${Math.floor(channel.createdTimestamp / 1000)}:F>`, inline: false }
            );

        if ('topic' in channel && channel.topic) {
            embed.addFields({ name: 'Topic', value: channel.topic, inline: false });
        }

        if ('nsfw' in channel) {
            embed.addFields({ name: 'NSFW', value: channel.nsfw ? 'Yes' : 'No', inline: true });
        }

        await interaction.reply({ embeds: [embed] });
    }
}
