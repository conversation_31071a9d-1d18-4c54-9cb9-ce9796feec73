import { CommandInteraction, PermissionFlagsBits, TextChannel } from 'discord.js';
import { Discord, Slash, SlashOption } from 'discordx';

@Discord()
export class ClearCommand {
    @Slash({
        description: 'Deletes a specified number of messages in the current channel',
        defaultMemberPermissions: PermissionFlagsBits.ManageMessages
    })
    async clear(
        @SlashOption({
            name: 'amount',
            description: 'Number of messages to delete (1-100)',
            type: 'INTEGER',
            minValue: 1,
            maxValue: 100,
            required: true
        })
        amount: number,
        interaction: CommandInteraction
    ): Promise<void> {
        const channel = interaction.channel;

        if (!channel || !(channel instanceof TextChannel)) {
            await interaction.reply({ content: 'This command can only be used in text channels!', ephemeral: true });
            return;
        }

        if (!interaction.memberPermissions?.has(PermissionFlagsBits.ManageMessages)) {
            await interaction.reply({ content: 'You need the "Manage Messages" permission to use this command!', ephemeral: true });
            return;
        }

        if (!channel.permissionsFor(interaction.client.user!)?.has(PermissionFlagsBits.ManageMessages)) {
            await interaction.reply({ content: 'I need the "Manage Messages" permission to delete messages!', ephemeral: true });
            return;
        }

        try {
            await interaction.deferReply({ ephemeral: true });

            const messages = await channel.bulkDelete(amount, true);
            const deletedCount = messages.size;

            await interaction.editReply(`Successfully deleted ${deletedCount} message${deletedCount === 1 ? '' : 's'}!`);
        } catch (error) {
            console.error('Error deleting messages:', error);
            
            const errorMessage = error instanceof Error && error.message.includes('older than 14 days')
                ? 'Cannot delete messages older than 14 days!'
                : 'An error occurred while deleting messages!';

            if (interaction.deferred) {
                await interaction.editReply(errorMessage);
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    }
}
