import { ChatInputCommandInteraction, SlashCommandBuilder, PermissionFlagsBits, TextChannel } from 'discord.js';
import { ICommand } from '../types/Command';

export default class ClearCommand implements ICommand {
    public data = new SlashCommandBuilder()
        .setName('clear')
        .setDescription('Deletes a specified number of messages in the current channel')
        .addIntegerOption(option =>
            option
                .setName('amount')
                .setDescription('Number of messages to delete (1-100)')
                .setRequired(true)
                .setMinValue(1)
                .setMaxValue(100)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageMessages);

    public async run(interaction: ChatInputCommandInteraction): Promise<void> {
        const amount = interaction.options.getInteger('amount', true);
        const channel = interaction.channel;

        if (!channel || !(channel instanceof TextChannel)) {
            await interaction.reply({ content: 'This command can only be used in text channels!', ephemeral: true });
            return;
        }

        if (!interaction.memberPermissions?.has(PermissionFlagsBits.ManageMessages)) {
            await interaction.reply({ content: 'You need the "Manage Messages" permission to use this command!', ephemeral: true });
            return;
        }

        if (!channel.permissionsFor(interaction.client.user!)?.has(PermissionFlagsBits.ManageMessages)) {
            await interaction.reply({ content: 'I need the "Manage Messages" permission to delete messages!', ephemeral: true });
            return;
        }

        try {
            await interaction.deferReply({ ephemeral: true });

            const messages = await channel.bulkDelete(amount, true);
            const deletedCount = messages.size;

            await interaction.editReply(`Successfully deleted ${deletedCount} message${deletedCount === 1 ? '' : 's'}!`);
        } catch (error) {
            console.error('Error deleting messages:', error);
            
            const errorMessage = error instanceof Error && error.message.includes('older than 14 days')
                ? 'Cannot delete messages older than 14 days!'
                : 'An error occurred while deleting messages!';

            if (interaction.deferred) {
                await interaction.editReply(errorMessage);
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    }
}
