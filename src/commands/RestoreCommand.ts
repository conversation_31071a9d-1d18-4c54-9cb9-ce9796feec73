import { CommandInteraction, PermissionFlagsBits, EmbedBuilder, ApplicationCommandOptionType } from 'discord.js';
import { Discord, Slash, SlashGroup, SlashOption } from 'discordx';
import { RestoreService } from '../services/RestoreService';

@Discord()
@SlashGroup({
    description: 'Restores data from JSON backups',
    name: 'restore',
    defaultMemberPermissions: PermissionFlagsBits.Administrator
})
export class RestoreCommand {
    private restoreService: RestoreService;

    constructor() {
        this.restoreService = new RestoreService();
    }

    @SlashGroup('restore')
    @Slash({ description: 'Lists available backup files for this server' })
    async list(interaction: CommandInteraction): Promise<void> {
        await this.handleRestoreList(interaction);
    }

    @SlashGroup('restore')
    @Slash({ description: 'Restores from a specific backup file' })
    async file(
        @SlashOption({
            name: 'filename',
            description: 'Name of the backup file to restore from',
            type: ApplicationCommandOptionType.String,
            required: true
        })
        filename: string,
        interaction: CommandInteraction
    ): Promise<void> {
        await this.handleRestoreFile(interaction, filename);
    }

    private async handleRestoreList(interaction: CommandInteraction): Promise<void> {
        const guild = interaction.guild;
        if (!this.validateGuildAndPermissions(interaction, guild)) return;

        await this.handleListBackups(interaction, guild!.id);
    }

    private async handleRestoreFile(interaction: CommandInteraction, filename: string): Promise<void> {
        const guild = interaction.guild;
        if (!this.validateGuildAndPermissions(interaction, guild)) return;

        await this.handleRestoreFromFile(interaction, guild!, filename);
    }

    private validateGuildAndPermissions(interaction: CommandInteraction, guild: any): boolean {
        if (!guild) {
            interaction.reply({ content: 'This command can only be used in a server!', ephemeral: true });
            return false;
        }

        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            interaction.reply({ content: 'You need Administrator permissions to use this command!', ephemeral: true });
            return false;
        }

        return true;
    }

    private async handleListBackups(interaction: CommandInteraction, guildId: string): Promise<void> {
        try {
            const backups = await this.restoreService.listAvailableBackups(guildId);
            
            if (backups.length === 0) {
                await interaction.reply({ content: 'No backup files found for this server!', ephemeral: true });
                return;
            }

            const embed = new EmbedBuilder()
                .setColor(0x0099FF)
                .setTitle('Available Backup Files')
                .setDescription('Here are the backup files available for this server:')
                .addFields(
                    backups.slice(0, 25).map((filename, index) => ({
                        name: `${index + 1}. ${filename}`,
                        value: `Use \`/restore file filename:${filename}\` to restore`,
                        inline: false
                    }))
                )
                .setTimestamp();

            if (backups.length > 25) {
                embed.setFooter({ text: `Showing first 25 of ${backups.length} backup files` });
            }

            await interaction.reply({ embeds: [embed] });
        } catch (error) {
            console.error('Error listing backups:', error);
            await interaction.reply({ content: 'An error occurred while listing backup files!', ephemeral: true });
        }
    }

    private async handleRestoreFromFile(interaction: CommandInteraction, guild: any, filename: string): Promise<void> {
        await interaction.deferReply();

        try {
            await this.restoreService.restoreFromFile(guild, filename);

            const embed = new EmbedBuilder()
                .setColor(0x00FF00)
                .setTitle('Restore Successful')
                .setDescription(`Data has been restored from backup file: ${filename}`)
                .addFields(
                    { name: 'Server', value: guild.name, inline: true },
                    { name: 'Restored At', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                )
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });
        } catch (error) {
            console.error('Restore error:', error);

            let errorMessage = 'An error occurred while restoring from the backup file!';
            if (error instanceof Error) {
                if (error.message.includes('different server')) {
                    errorMessage = 'This backup file is for a different server!';
                } else if (error.message.includes('not found')) {
                    errorMessage = 'Backup file not found!';
                }
            }

            await interaction.editReply(errorMessage);
        }
    }
}
