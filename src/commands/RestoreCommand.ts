import { ChatInputCommandInteraction, SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder } from 'discord.js';
import { ICommand } from '../types/Command';
import { RestoreService } from '../services/RestoreService';

export default class RestoreCommand implements ICommand {
    private restoreService: RestoreService;

    constructor() {
        this.restoreService = new RestoreService();
    }

    public data = new SlashCommandBuilder()
        .setName('restore')
        .setDescription('Restores data from JSON backups')
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('Lists available backup files for this server')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('file')
                .setDescription('Restores from a specific backup file')
                .addStringOption(option =>
                    option
                        .setName('filename')
                        .setDescription('Name of the backup file to restore from')
                        .setRequired(true)
                )
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator);

    public async run(interaction: ChatInputCommandInteraction): Promise<void> {
        const guild = interaction.guild;
        if (!guild) {
            await interaction.reply({ content: 'This command can only be used in a server!', ephemeral: true });
            return;
        }

        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            await interaction.reply({ content: 'You need Administrator permissions to use this command!', ephemeral: true });
            return;
        }

        const subcommand = interaction.options.getSubcommand();

        if (subcommand === 'list') {
            await this.handleListBackups(interaction, guild.id);
        } else if (subcommand === 'file') {
            await this.handleRestoreFromFile(interaction, guild);
        }
    }

    private async handleListBackups(interaction: ChatInputCommandInteraction, guildId: string): Promise<void> {
        try {
            const backups = await this.restoreService.listAvailableBackups(guildId);
            
            if (backups.length === 0) {
                await interaction.reply({ content: 'No backup files found for this server!', ephemeral: true });
                return;
            }

            const embed = new EmbedBuilder()
                .setColor(0x0099FF)
                .setTitle('Available Backup Files')
                .setDescription('Here are the backup files available for this server:')
                .addFields(
                    backups.slice(0, 25).map((filename, index) => ({
                        name: `${index + 1}. ${filename}`,
                        value: `Use \`/restore file filename:${filename}\` to restore`,
                        inline: false
                    }))
                )
                .setTimestamp();

            if (backups.length > 25) {
                embed.setFooter({ text: `Showing first 25 of ${backups.length} backup files` });
            }

            await interaction.reply({ embeds: [embed] });
        } catch (error) {
            console.error('Error listing backups:', error);
            await interaction.reply({ content: 'An error occurred while listing backup files!', ephemeral: true });
        }
    }

    private async handleRestoreFromFile(interaction: ChatInputCommandInteraction, guild: any): Promise<void> {
        const filename = interaction.options.getString('filename', true);
        
        await interaction.deferReply();

        try {
            await this.restoreService.restoreFromFile(guild, filename);

            const embed = new EmbedBuilder()
                .setColor(0x00FF00)
                .setTitle('Restore Successful')
                .setDescription(`Data has been restored from backup file: ${filename}`)
                .addFields(
                    { name: 'Server', value: guild.name, inline: true },
                    { name: 'Restored At', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                )
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });
        } catch (error) {
            console.error('Restore error:', error);
            
            let errorMessage = 'An error occurred while restoring from the backup file!';
            if (error instanceof Error) {
                if (error.message.includes('different server')) {
                    errorMessage = 'This backup file is for a different server!';
                } else if (error.message.includes('not found')) {
                    errorMessage = 'Backup file not found!';
                }
            }

            await interaction.editReply(errorMessage);
        }
    }
}
