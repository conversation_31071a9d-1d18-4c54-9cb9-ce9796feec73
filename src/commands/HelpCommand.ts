import { CommandInteraction, EmbedBuilder } from 'discord.js';
import { Discord, Slash } from 'discordx';

@Discord()
export class HelpCommand {
    @Slash({ description: 'Lists all available commands with their descriptions' })
    async help(interaction: CommandInteraction): Promise<void> {
        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle('Available Commands')
            .setDescription('Here are all the commands you can use:')
            .addFields(
                { name: '/ping', value: 'Checks if the bot is active and shows response time', inline: false },
                { name: '/help', value: 'Lists all available commands with their descriptions', inline: false },
                { name: '/status profile', value: 'Shows bot username, ID, and creation date', inline: false },
                { name: '/status server', value: 'Shows server name, member count, and creation date', inline: false },
                { name: '/status channel', value: 'Shows details of the current channel', inline: false },
                { name: '/clear <amount>', value: 'Deletes a specified number of messages (requires Manage Messages permission)', inline: false },
                { name: '/backup all', value: 'Backs up all server data to JSON files', inline: false },
                { name: '/backup channel', value: 'Backs up channel settings and messages', inline: false },
                { name: '/backup emoji', value: 'Backs up custom emojis', inline: false },
                { name: '/backup provice', value: 'Backs up server privacy settings', inline: false },
                { name: '/backup chat', value: 'Backs up messages in specified channels', inline: false },
                { name: '/backup role', value: 'Backs up role settings', inline: false },
                { name: '/backup status', value: 'Backs up server status information', inline: false },
                { name: '/restore <type>', value: 'Restores data from JSON backups', inline: false }
            )
            .setTimestamp()
            .setFooter({ text: 'Hikacord Bot', iconURL: interaction.client.user?.displayAvatarURL() });

        await interaction.reply({ embeds: [embed] });
    }
}
