import { CommandInteraction } from 'discord.js';
import { Discord, Slash } from 'discordx';

@Discord()
export class PingCommand {
    @Slash({ description: 'Checks if the bot is active and shows response time' })
    async ping(interaction: CommandInteraction): Promise<void> {
        const sent = await interaction.reply({ content: 'Pinging...', fetchReply: true });
        const timeDiff = sent.createdTimestamp - interaction.createdTimestamp;

        await interaction.editReply(`Pong! 🏓 ${timeDiff}ms`);
    }
}
