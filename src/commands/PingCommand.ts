import { ChatInputCommandInteraction, SlashCommandBuilder } from 'discord.js';
import { ICommand } from '../types/Command';

export default class PingCommand implements ICommand {
    public data = new SlashCommandBuilder()
        .setName('ping')
        .setDescription('Checks if the bot is active and shows response time');

    public async run(interaction: ChatInputCommandInteraction): Promise<void> {
        const sent = await interaction.reply({ content: 'Pinging...', fetchReply: true });
        const timeDiff = sent.createdTimestamp - interaction.createdTimestamp;
        
        await interaction.editReply(`Pong! 🏓 ${timeDiff}ms`);
    }
}
