import { config } from 'dotenv';
import { BotClient } from './client/BotClient';
import './commands/PingCommand';
import './commands/HelpCommand';
import './commands/StatusCommand';
import './commands/ClearCommand';
import './commands/BackupCommand';
import './commands/RestoreCommand';

config();

const token = process.env['DISCORD_TOKEN'];
const clientId = process.env['CLIENT_ID'];

if (!token || !clientId) {
    console.error('Missing required environment variables: DISCORD_TOKEN and CLIENT_ID');
    process.exit(1);
}

const bot = new BotClient();

bot.start(token).catch(error => {
    console.error('Failed to start bot:', error);
    process.exit(1);
});

process.on('SIGINT', () => {
    console.log('Shutting down bot...');
    bot.destroy();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('Shutting down bot...');
    bot.destroy();
    process.exit(0);
});
