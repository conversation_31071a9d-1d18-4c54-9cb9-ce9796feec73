import { Client, Collection, GatewayIntentBits, REST, Routes, ChatInputCommandInteraction } from 'discord.js';
import { readdirSync } from 'fs';
import { join } from 'path';
import { ICommand } from '../types/Command';
import { AutoBackupService } from '../services/AutoBackupService';

export class BotClient extends Client {
    public commands: Collection<string, ICommand>;
    private token: string;
    private clientId: string;
    private autoBackupService: AutoBackupService;

    constructor(token: string, clientId: string) {
        super({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.GuildMembers
            ]
        });

        this.commands = new Collection();
        this.token = token;
        this.clientId = clientId;
        this.autoBackupService = new AutoBackupService(this);

        this.setupEventHandlers();
    }

    private setupEventHandlers(): void {
        this.once('ready', () => {
            console.log(`Bot is ready! Logged in as ${this.user?.tag}`);
            this.autoBackupService.start();
        });

        this.on('interactionCreate', async (interaction) => {
            if (!interaction.isChatInputCommand()) return;

            const command = this.commands.get(interaction.commandName);
            if (!command) return;

            try {
                await command.run(interaction as ChatInputCommandInteraction);
            } catch (error) {
                console.error('Error executing command:', error);
                const reply = { content: 'There was an error executing this command!', ephemeral: true };
                
                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp(reply);
                } else {
                    await interaction.reply(reply);
                }
            }
        });
    }

    public async loadCommands(): Promise<void> {
        const commandsPath = join(__dirname, '../commands');
        const commandFiles = readdirSync(commandsPath).filter(file => file.endsWith('.js') || file.endsWith('.ts'));

        for (const file of commandFiles) {
            const filePath = join(commandsPath, file);
            const commandModule = await import(filePath);
            const command: ICommand = new commandModule.default();

            if ('data' in command && 'run' in command) {
                this.commands.set(command.data.name, command);
            } else {
                console.log(`Warning: The command at ${filePath} is missing a required "data" or "run" property.`);
            }
        }
    }

    public async registerCommands(): Promise<void> {
        const commands = Array.from(this.commands.values()).map(command => command.data.toJSON());
        const rest = new REST().setToken(this.token);

        try {
            console.log(`Started refreshing ${commands.length} application (/) commands.`);

            await rest.put(
                Routes.applicationCommands(this.clientId),
                { body: commands }
            );

            console.log(`Successfully reloaded ${commands.length} application (/) commands.`);
        } catch (error) {
            console.error('Error registering commands:', error);
        }
    }

    public async start(): Promise<void> {
        await this.loadCommands();
        await this.registerCommands();
        await this.login(this.token);
    }

    public getAutoBackupService(): AutoBackupService {
        return this.autoBackupService;
    }
}
