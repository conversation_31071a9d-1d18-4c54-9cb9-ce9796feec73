import { Client } from 'discordx';
import { GatewayIntentBits } from 'discord.js';
import { AutoBackupService } from '../services/AutoBackupService';

export class BotClient extends Client {
    private autoBackupService: AutoBackupService;

    constructor() {
        super({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.GuildMembers
            ],
        });

        this.autoBackupService = new AutoBackupService(this);
    }

    public async start(token: string): Promise<void> {
        await this.login(token);

        this.once('ready', async () => {
            await this.initApplicationCommands();
            console.log('Bot started');
            this.autoBackupService.start();
        });

        this.on('interactionCreate', (interaction) => {
            this.executeInteraction(interaction);
        });
    }

    public getAutoBackupService(): AutoBackupService {
        return this.autoBackupService;
    }
}
