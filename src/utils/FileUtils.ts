import { promises as fs } from 'fs';
import { join } from 'path';

export class FileUtils {
    private static backupDir = join(process.cwd(), 'backups');

    public static async ensureBackupDirectory(): Promise<void> {
        try {
            await fs.access(this.backupDir);
        } catch {
            await fs.mkdir(this.backupDir, { recursive: true });
        }
    }

    public static async saveBackup(filename: string, data: any): Promise<void> {
        await this.ensureBackupDirectory();
        const filePath = join(this.backupDir, filename);
        await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    }

    public static async loadBackup(filename: string): Promise<any> {
        const filePath = join(this.backupDir, filename);
        const data = await fs.readFile(filePath, 'utf-8');
        return JSON.parse(data);
    }

    public static async listBackups(): Promise<string[]> {
        try {
            await this.ensureBackupDirectory();
            const files = await fs.readdir(this.backupDir);
            return files.filter(file => file.endsWith('.json'));
        } catch {
            return [];
        }
    }

    public static generateBackupFilename(guildId: string, type: string): string {
        const timestamp = new Date().toISOString().split('T')[0];
        return `${type}-${guildId}-${timestamp}.json`;
    }
}
