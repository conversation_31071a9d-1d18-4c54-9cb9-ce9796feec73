export interface BackupData {
    guildId: string;
    guildName: string;
    timestamp: string;
    type: BackupType;
    data: any;
}

export enum BackupType {
    ALL = 'all',
    CHANNEL = 'channel',
    EMOJI = 'emoji',
    PROVICE = 'provice',
    CHAT = 'chat',
    ROLE = 'role',
    STATUS = 'status'
}

export interface ChannelBackup {
    id: string;
    name: string;
    type: number;
    topic?: string;
    nsfw?: boolean;
    position: number;
    parentId?: string;
    permissionOverwrites: any[];
}

export interface RoleBackup {
    id: string;
    name: string;
    color: number;
    hoist: boolean;
    mentionable: boolean;
    permissions: string;
    position: number;
}

export interface EmojiBackup {
    id: string;
    name: string;
    url: string;
    animated: boolean;
}

export interface MessageBackup {
    id: string;
    content: string;
    author: {
        id: string;
        username: string;
        discriminator: string;
    };
    timestamp: string;
    attachments: any[];
    embeds: any[];
}
