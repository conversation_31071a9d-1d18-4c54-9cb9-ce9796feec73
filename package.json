{"name": "hikacord-bot", "version": "1.0.0", "description": "A professional Discord bot built with discord.js and TypeScript", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["discord", "bot", "typescript", "discord.js"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"all": "^0.0.0", "discord.js": "^14.21.0", "discordx": "^11.12.5", "dotenv": "^16.6.1", "node-cron": "^3.0.3"}, "devDependencies": {"@types/node": "^20.19.5", "@types/node-cron": "^3.0.11", "rimraf": "^5.0.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}}